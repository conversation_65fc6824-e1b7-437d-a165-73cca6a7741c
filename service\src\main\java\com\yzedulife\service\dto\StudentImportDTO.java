package com.yzedulife.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 学生导入DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StudentImportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 行号（用于错误定位）
     */
    private Integer rowNumber;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 学号
     */
    private String studentNumber;

    /**
     * 姓名
     */
    private String name;
}
