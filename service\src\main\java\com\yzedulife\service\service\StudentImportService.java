package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentImportDTO;

import java.io.InputStream;
import java.util.List;

/**
 * 学生导入服务接口
 */
public interface StudentImportService {

    /**
     * 从Excel文件导入学生数据
     * @param inputStream Excel文件输入流
     * @param fileName 文件名
     * @return 导入结果列表，包含成功和失败的记录
     */
    List<StudentImportDTO> importStudentsFromExcel(InputStream inputStream, String fileName) throws BusinessException;

    /**
     * 处理学生导入数据，创建学校、班级和学生
     * @param importDataList 导入的数据列表
     * @return 处理结果列表，包含成功和失败的记录
     */
    List<StudentImportDTO> processImportData(List<StudentImportDTO> importDataList) throws BusinessException;
}
